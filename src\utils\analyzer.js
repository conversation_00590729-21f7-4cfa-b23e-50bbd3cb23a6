const fs = require("fs-extra");
const path = require("path");
const chalk = require("chalk");
const { format, subDays } = require("date-fns");
const _ = require("lodash");

class Analyzer {
  constructor() {
    this.logsDir = path.join(process.cwd(), "logs");
    this.reportsDir = path.join(this.logsDir, "reports");
  }

  async analyze(options = {}) {
    console.log(chalk.cyan("📊 Starting test results analysis...\n"));

    const { file, days = 7 } = options;

    let results;
    if (file) {
      results = await this.analyzeFile(file);
    } else {
      results = await this.analyzeRecentResults(days);
    }

    this.displayAnalysis(results);
    return results;
  }

  async analyzeFile(filePath) {
    try {
      const fullPath = path.isAbsolute(filePath)
        ? filePath
        : path.join(this.logsDir, filePath);

      if (!(await fs.pathExists(fullPath))) {
        throw new Error(`File not found: ${fullPath}`);
      }

      const data = await fs.readJson(fullPath);
      return this.analyzeTestResult(data);
    } catch (error) {
      throw new Error(`Failed to analyze file: ${error.message}`);
    }
  }

  async analyzeRecentResults(days) {
    try {
      const files = await this.getResultFiles(days);

      if (files.length === 0) {
        throw new Error(`No test results found in the last ${days} days`);
      }

      console.log(
        chalk.blue(
          `Found ${files.length} test results from the last ${days} days\n`
        )
      );

      const analyses = await Promise.all(
        files.map(async (file) => {
          try {
            const data = await fs.readJson(file.path);
            return {
              ...this.analyzeTestResult(data),
              filename: file.name,
              date: file.date,
            };
          } catch (error) {
            console.warn(
              chalk.yellow(
                `⚠️  Could not analyze ${file.name}: ${error.message}`
              )
            );
            return null;
          }
        })
      );

      return this.aggregateAnalyses(analyses.filter(Boolean));
    } catch (error) {
      throw new Error(`Failed to analyze recent results: ${error.message}`);
    }
  }

  async getResultFiles(days) {
    const cutoffDate = subDays(new Date(), days);
    const files = await fs.readdir(this.logsDir);

    const resultFiles = await Promise.all(
      files
        .filter((file) => file.endsWith(".json") && !file.includes("config"))
        .map(async (file) => {
          const filePath = path.join(this.logsDir, file);
          const stats = await fs.stat(filePath);

          if (stats.mtime >= cutoffDate) {
            return {
              name: file,
              path: filePath,
              date: stats.mtime,
            };
          }
          return null;
        })
    );

    return resultFiles.filter(Boolean).sort((a, b) => b.date - a.date);
  }

  analyzeTestResult(data) {
    const { stats, metadata, errors } = data;

    if (!stats) {
      throw new Error("Invalid test result format - missing stats");
    }

    const analysis = {
      summary: {
        scenario: metadata?.testConfig?.scenario || "unknown",
        timestamp: metadata?.timestamp,
        duration: stats.duration,
        totalRequests: stats.totalRequests,
        successRate: stats.successRate,
        errorRate: stats.errorRate,
        requestsPerSecond: stats.requestsPerSecond,
        avgResponseTime: stats.avgResponseTime,
      },
      performance: this.analyzePerformance(stats),
      reliability: this.analyzeReliability(stats, errors),
      trends: this.analyzeTrends(data),
      issues: this.identifyIssues(stats, errors),
      score: this.calculateScore(stats),
    };

    return analysis;
  }

  analyzePerformance(stats) {
    const performance = {
      rating: "good",
      metrics: {
        throughput: stats.requestsPerSecond,
        latency: stats.avgResponseTime,
        responseTimeP95: stats.responseTimePercentiles?.p95 || 0,
        responseTimeP99: stats.responseTimePercentiles?.p99 || 0,
      },
    };

    // Performance rating logic
    if (stats.avgResponseTime > 5000) {
      performance.rating = "poor";
    } else if (stats.avgResponseTime > 2000) {
      performance.rating = "fair";
    } else if (stats.avgResponseTime > 1000) {
      performance.rating = "good";
    } else {
      performance.rating = "excellent";
    }

    performance.recommendations = [];

    if (stats.avgResponseTime > 2000) {
      performance.recommendations.push(
        "Consider API optimization - response times are high"
      );
    }

    if (stats.requestsPerSecond < 10) {
      performance.recommendations.push(
        "Low throughput - consider infrastructure scaling"
      );
    }

    return performance;
  }

  analyzeReliability(stats, errors) {
    const reliability = {
      rating: "excellent",
      successRate: stats.successRate,
      errorAnalysis: {},
      recommendations: [],
    };

    if (errors?.summary) {
      reliability.errorAnalysis = errors.summary;
    }

    // Reliability rating
    if (stats.successRate < 90) {
      reliability.rating = "poor";
      reliability.recommendations.push(
        "Critical: Success rate below 90% - investigate API errors"
      );
    } else if (stats.successRate < 95) {
      reliability.rating = "fair";
      reliability.recommendations.push(
        "Success rate below 95% - monitor for issues"
      );
    } else if (stats.successRate < 99) {
      reliability.rating = "good";
    }

    // Common error patterns
    if (reliability.errorAnalysis) {
      const commonErrors = Object.entries(reliability.errorAnalysis)
        .sort(([, a], [, b]) => b - a)
        .slice(0, 3);

      if (commonErrors.length > 0) {
        reliability.recommendations.push(
          `Most common error: ${commonErrors[0][0]} (${commonErrors[0][1]} occurrences)`
        );
      }
    }

    return reliability;
  }

  analyzeTrends(data) {
    // This would be enhanced with historical data comparison
    return {
      note: "Trend analysis requires historical data comparison",
      batches:
        data.batches?.map((batch) => ({
          batchNumber: batch.batchNumber,
          successRate: (batch.successful / batch.emails) * 100,
          avgResponseTime: batch.avgResponseTime,
        })) || [],
    };
  }

  identifyIssues(stats, errors) {
    const issues = [];

    // Performance issues
    if (stats.avgResponseTime > 3000) {
      issues.push({
        type: "performance",
        severity: "high",
        message: `High average response time: ${stats.avgResponseTime.toFixed(
          2
        )}ms`,
      });
    }

    // Reliability issues
    if (stats.successRate < 95) {
      issues.push({
        type: "reliability",
        severity: "high",
        message: `Low success rate: ${stats.successRate.toFixed(2)}%`,
      });
    }

    // Throughput issues
    if (stats.requestsPerSecond < 5) {
      issues.push({
        type: "throughput",
        severity: "medium",
        message: `Low throughput: ${stats.requestsPerSecond.toFixed(2)} req/s`,
      });
    }

    // Error pattern issues
    if (errors?.summary) {
      const totalErrors = Object.values(errors.summary).reduce(
        (a, b) => a + b,
        0
      );
      if (totalErrors > stats.totalRequests * 0.1) {
        issues.push({
          type: "errors",
          severity: "medium",
          message: `High error count: ${totalErrors} errors (${(
            (totalErrors / stats.totalRequests) *
            100
          ).toFixed(2)}%)`,
        });
      }
    }

    return issues;
  }

  calculateScore(stats) {
    let score = 100;

    // Success rate impact (40% weight)
    const successPenalty = (100 - stats.successRate) * 0.4;
    score -= successPenalty;

    // Response time impact (30% weight)
    const responseTimePenalty =
      Math.max(0, (stats.avgResponseTime - 1000) / 100) * 0.3;
    score -= responseTimePenalty;

    // Throughput impact (20% weight)
    const throughputPenalty =
      Math.max(0, (10 - stats.requestsPerSecond) * 2) * 0.2;
    score -= throughputPenalty;

    // Error rate impact (10% weight)
    const errorPenalty = stats.errorRate * 0.1;
    score -= errorPenalty;

    return Math.max(0, Math.min(100, score));
  }

  aggregateAnalyses(analyses) {
    if (analyses.length === 0) {
      throw new Error("No valid analyses to aggregate");
    }

    const aggregate = {
      summary: {
        totalTests: analyses.length,
        dateRange: {
          from: new Date(
            Math.min(...analyses.map((a) => new Date(a.date)))
          ).toISOString(),
          to: new Date(
            Math.max(...analyses.map((a) => new Date(a.date)))
          ).toISOString(),
        },
        scenarios: _.countBy(analyses, "summary.scenario"),
        avgScore: _.meanBy(analyses, "score"),
        totalRequests: _.sumBy(analyses, "summary.totalRequests"),
        avgSuccessRate: _.meanBy(analyses, "summary.successRate"),
        avgResponseTime: _.meanBy(analyses, "summary.avgResponseTime"),
      },
      trends: this.calculateTrends(analyses),
      issues: this.aggregateIssues(analyses),
      recommendations: this.generateAggregateRecommendations(analyses),
      individual: analyses,
    };

    return aggregate;
  }

  calculateTrends(analyses) {
    const sorted = analyses.sort((a, b) => new Date(a.date) - new Date(b.date));

    const trends = {
      successRate: this.calculateTrend(
        sorted.map((a) => a.summary.successRate)
      ),
      responseTime: this.calculateTrend(
        sorted.map((a) => a.summary.avgResponseTime)
      ),
      throughput: this.calculateTrend(
        sorted.map((a) => a.summary.requestsPerSecond)
      ),
      score: this.calculateTrend(sorted.map((a) => a.score)),
    };

    return trends;
  }

  calculateTrend(values) {
    if (values.length < 2) return { direction: "stable", change: 0 };

    const first = values[0];
    const last = values[values.length - 1];
    const change = ((last - first) / first) * 100;

    let direction = "stable";
    if (Math.abs(change) > 5) {
      direction = change > 0 ? "improving" : "declining";
    }

    return { direction, change: change.toFixed(2) };
  }

  aggregateIssues(analyses) {
    const allIssues = analyses.flatMap((a) => a.issues || []);
    const issuesByType = _.groupBy(allIssues, "type");

    return Object.entries(issuesByType).map(([type, issues]) => ({
      type,
      count: issues.length,
      severity:
        _.maxBy(issues, (i) =>
          i.severity === "high" ? 3 : i.severity === "medium" ? 2 : 1
        )?.severity || "low",
      examples: issues.slice(0, 3).map((i) => i.message),
    }));
  }

  generateAggregateRecommendations(analyses) {
    const recommendations = [];
    const avgScore = _.meanBy(analyses, "score");
    const avgSuccessRate = _.meanBy(analyses, "summary.successRate");
    const avgResponseTime = _.meanBy(analyses, "summary.avgResponseTime");

    if (avgScore < 70) {
      recommendations.push(
        "Overall performance is below acceptable levels - comprehensive review needed"
      );
    }

    if (avgSuccessRate < 95) {
      recommendations.push(
        "Success rate consistently below 95% - investigate API stability"
      );
    }

    if (avgResponseTime > 2000) {
      recommendations.push(
        "Response times are consistently high - consider performance optimization"
      );
    }

    const scenarios = _.countBy(analyses, "summary.scenario");
    const worstScenario = Object.entries(scenarios).sort(
      ([, a], [, b]) => b - a
    )[0];

    if (worstScenario) {
      recommendations.push(
        `Most tested scenario: ${worstScenario[0]} - ensure it represents real usage patterns`
      );
    }

    return recommendations;
  }

  displayAnalysis(analysis) {
    if (analysis.summary?.totalTests) {
      this.displayAggregateAnalysis(analysis);
    } else {
      this.displaySingleAnalysis(analysis);
    }
  }

  displaySingleAnalysis(analysis) {
    console.log(chalk.cyan("📊 TEST ANALYSIS RESULTS"));
    console.log(chalk.gray("─".repeat(60)));

    const { summary, performance, reliability, score } = analysis;

    // Summary
    console.log(chalk.blue("\n📋 Summary:"));
    console.log(`  Scenario: ${chalk.yellow(summary.scenario)}`);
    console.log(`  Score: ${this.colorizeScore(score.toFixed(1))}/100`);
    console.log(
      `  Duration: ${chalk.white((summary.duration / 1000).toFixed(2))}s`
    );
    console.log(`  Total Requests: ${chalk.white(summary.totalRequests)}`);
    console.log(
      `  Success Rate: ${this.colorizeRate(summary.successRate.toFixed(2))}%`
    );
    console.log(
      `  Avg Response Time: ${chalk.white(
        summary.avgResponseTime.toFixed(2)
      )}ms`
    );

    // Performance
    console.log(chalk.blue("\n⚡ Performance:"));
    console.log(`  Rating: ${this.colorizeRating(performance.rating)}`);
    console.log(
      `  Throughput: ${chalk.white(
        performance.metrics.throughput.toFixed(2)
      )} req/s`
    );
    console.log(
      `  95th Percentile: ${chalk.white(performance.metrics.responseTimeP95)}ms`
    );

    // Reliability
    console.log(chalk.blue("\n🛡️  Reliability:"));
    console.log(`  Rating: ${this.colorizeRating(reliability.rating)}`);
    console.log(
      `  Success Rate: ${this.colorizeRate(
        reliability.successRate.toFixed(2)
      )}%`
    );

    // Issues
    if (analysis.issues.length > 0) {
      console.log(chalk.red("\n⚠️  Issues Found:"));
      analysis.issues.forEach((issue) => {
        const color =
          issue.severity === "high"
            ? "red"
            : issue.severity === "medium"
            ? "yellow"
            : "gray";
        console.log(chalk[color](`  • ${issue.message}`));
      });
    }

    // Recommendations
    const allRecommendations = [
      ...(performance.recommendations || []),
      ...(reliability.recommendations || []),
    ];

    if (allRecommendations.length > 0) {
      console.log(chalk.blue("\n💡 Recommendations:"));
      allRecommendations.forEach((rec) => {
        console.log(chalk.white(`  • ${rec}`));
      });
    }
  }

  displayAggregateAnalysis(analysis) {
    console.log(chalk.cyan("📊 AGGREGATE ANALYSIS RESULTS"));
    console.log(chalk.gray("─".repeat(60)));

    const { summary, trends, issues, recommendations } = analysis;

    // Summary
    console.log(chalk.blue("\n📋 Summary:"));
    console.log(`  Total Tests: ${chalk.yellow(summary.totalTests)}`);
    console.log(
      `  Date Range: ${chalk.white(
        summary.dateRange.from.split("T")[0]
      )} to ${chalk.white(summary.dateRange.to.split("T")[0])}`
    );
    console.log(
      `  Average Score: ${this.colorizeScore(summary.avgScore.toFixed(1))}/100`
    );
    console.log(`  Total Requests: ${chalk.white(summary.totalRequests)}`);
    console.log(
      `  Avg Success Rate: ${this.colorizeRate(
        summary.avgSuccessRate.toFixed(2)
      )}%`
    );

    // Scenarios
    console.log(chalk.blue("\n🎯 Scenarios Tested:"));
    Object.entries(summary.scenarios).forEach(([scenario, count]) => {
      console.log(`  ${chalk.white(scenario)}: ${chalk.yellow(count)} tests`);
    });

    // Trends
    console.log(chalk.blue("\n📈 Trends:"));
    Object.entries(trends).forEach(([metric, trend]) => {
      const arrow =
        trend.direction === "improving"
          ? "↗️"
          : trend.direction === "declining"
          ? "↘️"
          : "➡️";
      console.log(
        `  ${metric}: ${arrow} ${trend.direction} (${trend.change}%)`
      );
    });

    // Top Issues
    if (issues.length > 0) {
      console.log(chalk.red("\n⚠️  Top Issues:"));
      issues.slice(0, 5).forEach((issue) => {
        const color =
          issue.severity === "high"
            ? "red"
            : issue.severity === "medium"
            ? "yellow"
            : "gray";
        console.log(
          chalk[color](`  • ${issue.type}: ${issue.count} occurrences`)
        );
      });
    }

    // Recommendations
    if (recommendations.length > 0) {
      console.log(chalk.blue("\n💡 Key Recommendations:"));
      recommendations.forEach((rec) => {
        console.log(chalk.white(`  • ${rec}`));
      });
    }
  }

  colorizeScore(score) {
    const num = parseFloat(score);
    if (num >= 90) return chalk.green(score);
    if (num >= 70) return chalk.yellow(score);
    return chalk.red(score);
  }

  colorizeRate(rate) {
    const num = parseFloat(rate);
    if (num >= 95) return chalk.green(rate);
    if (num >= 90) return chalk.yellow(rate);
    return chalk.red(rate);
  }

  colorizeRating(rating) {
    const colors = {
      excellent: "green",
      good: "green",
      fair: "yellow",
      poor: "red",
    };
    return chalk[colors[rating] || "white"](rating);
  }

  async exportAnalysis(analysis, format = "json") {
    const timestamp = format(new Date(), "yyyy-MM-dd_HH-mm-ss");
    await fs.ensureDir(this.reportsDir);

    if (format === "json") {
      const filePath = path.join(this.reportsDir, `analysis-${timestamp}.json`);
      await fs.writeJson(filePath, analysis, { spaces: 2 });
      console.log(chalk.gray(`\n💾 Analysis saved to: ${filePath}`));
      return filePath;
    }

    // Additional formats could be added here (CSV, HTML, etc.)
    throw new Error(`Unsupported format: ${format}`);
  }
}

// CLI interface
if (require.main === module) {
  const analyzer = new Analyzer();

  const args = process.argv.slice(2);
  const options = {};

  for (let i = 0; i < args.length; i += 2) {
    const key = args[i].replace("--", "");
    const value = args[i + 1];

    if (key === "file") options.file = value;
    if (key === "days") options.days = parseInt(value);
  }

  analyzer.analyze(options).catch((error) => {
    console.error(chalk.red("❌ Analysis failed:"), error.message);
    process.exit(1);
  });
}

module.exports = Analyzer;
