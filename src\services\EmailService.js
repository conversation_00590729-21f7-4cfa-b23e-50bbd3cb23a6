const fs = require("fs-extra");
const path = require("path");
const config = require("../../config");

class EmailService {
  constructor() {
    this.emailList = null;
    this.usedEmails = new Set();
  }

  async loadEmails() {
    if (this.emailList) {
      return this.emailList;
    }

    try {
      // Try to load from new data directory first
      const dataPath = path.join(process.cwd(), "data", "emails.json");
      const legacyPath = path.join(process.cwd(), "mail-list.js");

      if (await fs.pathExists(dataPath)) {
        this.emailList = await fs.readJson(dataPath);
      } else if (await fs.pathExists(legacyPath)) {
        // Load from legacy format
        delete require.cache[require.resolve(legacyPath)];
        this.emailList = require(legacyPath);

        // Convert and save to new format
        await this.saveEmailsToJson(this.emailList);
      } else {
        throw new Error(
          "No email list found. Please ensure emails.json exists in the data directory."
        );
      }

      // Validate email format
      this.emailList = this.emailList.filter((email) =>
        this.isValidEmail(email)
      );

      console.log(`📧 Loaded ${this.emailList.length} valid emails`);
      return this.emailList;
    } catch (error) {
      throw new Error(`Failed to load email list: ${error.message}`);
    }
  }

  async getEmails(maxCount = null) {
    await this.loadEmails();

    const availableEmails = this.emailList.filter(
      (email) => !this.usedEmails.has(email)
    );

    if (availableEmails.length === 0) {
      // Reset used emails if we've exhausted the list
      this.usedEmails.clear();
      console.log("🔄 Reset email usage - starting over with full list");
      return this.getEmails(maxCount);
    }

    const emailsToUse = maxCount
      ? availableEmails.slice(0, maxCount)
      : availableEmails;

    // Mark emails as used
    emailsToUse.forEach((email) => this.usedEmails.add(email));

    return emailsToUse;
  }

  async addEmails(newEmails) {
    await this.loadEmails();

    const validEmails = newEmails.filter((email) => this.isValidEmail(email));
    const uniqueEmails = validEmails.filter(
      (email) => !this.emailList.includes(email)
    );

    this.emailList.push(...uniqueEmails);
    await this.saveEmailsToJson(this.emailList);

    console.log(`➕ Added ${uniqueEmails.length} new unique emails`);
    return uniqueEmails.length;
  }

  async removeEmail(emailToRemove) {
    await this.loadEmails();

    const initialLength = this.emailList.length;
    this.emailList = this.emailList.filter((email) => email !== emailToRemove);

    if (this.emailList.length < initialLength) {
      await this.saveEmailsToJson(this.emailList);
      this.usedEmails.delete(emailToRemove);
      console.log(`🗑️  Removed email: ${emailToRemove}`);
      return true;
    }

    return false;
  }

  async getEmailStats() {
    await this.loadEmails();

    const domains = {};
    this.emailList.forEach((email) => {
      const domain = email.split("@")[1];
      domains[domain] = (domains[domain] || 0) + 1;
    });

    return {
      total: this.emailList.length,
      used: this.usedEmails.size,
      available: this.emailList.length - this.usedEmails.size,
      domains: Object.keys(domains).length,
      topDomains: Object.entries(domains)
        .sort(([, a], [, b]) => b - a)
        .slice(0, 10)
        .map(([domain, count]) => ({ domain, count })),
    };
  }

  async saveEmailsToJson(emails) {
    const dataDir = path.join(process.cwd(), "data");
    const filePath = path.join(dataDir, "emails.json");

    await fs.ensureDir(dataDir);
    await fs.writeJson(filePath, emails, { spaces: 2 });
  }

  isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  generateRandomEmail(domain = "test.com") {
    const randomString = Math.random().toString(36).substring(2, 15);
    return `test_${randomString}@${domain}`;
  }

  async generateTestEmails(
    count = 100,
    domains = ["test.com", "example.org", "demo.net"]
  ) {
    const emails = [];

    for (let i = 0; i < count; i++) {
      const domain = domains[Math.floor(Math.random() * domains.length)];
      const email = this.generateRandomEmail(domain);
      emails.push(email);
    }

    await this.addEmails(emails);
    return emails;
  }

  async importFromCsv(csvPath) {
    try {
      const csvContent = await fs.readFile(csvPath, "utf-8");
      const lines = csvContent.split("\n");
      const emails = [];

      lines.forEach((line, index) => {
        const trimmedLine = line.trim();
        if (trimmedLine && this.isValidEmail(trimmedLine)) {
          emails.push(trimmedLine);
        } else if (trimmedLine) {
          console.warn(
            `⚠️  Invalid email on line ${index + 1}: ${trimmedLine}`
          );
        }
      });

      const added = await this.addEmails(emails);
      console.log(`📁 Imported ${added} emails from CSV`);
      return added;
    } catch (error) {
      throw new Error(`Failed to import from CSV: ${error.message}`);
    }
  }

  async exportToCsv(outputPath = null) {
    await this.loadEmails();

    const csvPath =
      outputPath || path.join(process.cwd(), "data", "emails-export.csv");
    const csvContent = this.emailList.join("\n");

    await fs.ensureDir(path.dirname(csvPath));
    await fs.writeFile(csvPath, csvContent);

    console.log(`📤 Exported ${this.emailList.length} emails to ${csvPath}`);
    return csvPath;
  }

  resetUsage() {
    this.usedEmails.clear();
    console.log("🔄 Reset email usage tracking");
  }
}

module.exports = EmailService;
