{"name": "automated-api-load-tester", "version": "2.0.0", "description": "Automated API load testing tool with scheduling and monitoring capabilities", "main": "src/index.js", "scripts": {"start": "node src/index.js", "test": "node src/index.js --scenario smoke", "test:light": "node src/index.js --scenario light", "test:medium": "node src/index.js --scenario medium", "test:heavy": "node src/index.js --scenario heavy", "test:stress": "node src/index.js --scenario stress", "schedule": "node src/index.js schedule", "monitor": "node src/index.js monitor", "analyze": "node src/index.js analyze", "convert-emails": "node src/utils/convert-emails.js", "dev": "nodemon src/index.js", "quick-start": "node quick-start.js", "health": "curl http://localhost:3000/health || echo 'Dashboard not running'"}, "keywords": ["load-testing", "api-testing", "performance-testing", "automation", "monitoring", "scheduler"], "author": "API Load Test Automation", "license": "MIT", "engines": {"node": ">=14.0.0"}, "dependencies": {"axios": "^1.5.0", "chalk": "^4.1.2", "commander": "^11.0.0", "cron": "^3.1.0", "date-fns": "^2.30.0", "dotenv": "^16.3.0", "express": "^4.18.0", "fs-extra": "^11.1.0", "lodash": "^4.17.21", "winston": "^3.10.0"}, "devDependencies": {"nodemon": "^3.0.0"}}