const chalk = require("chalk");
const fs = require("fs-extra");
const path = require("path");
const { format } = require("date-fns");

const ApiService = require("../services/ApiService");
const EmailService = require("../services/EmailService");
const ReportService = require("../services/ReportService");
const config = require("../../config");
const logger = require("../utils/logger");

class LoadTestController {
  constructor() {
    this.apiService = new ApiService();
    this.emailService = new EmailService();
    this.reportService = new ReportService();
    this.isRunning = false;
  }

  async runTest(options = {}) {
    if (this.isRunning) {
      throw new Error("Load test is already running");
    }

    this.isRunning = true;
    const startTime = Date.now();

    try {
      // Merge options with config
      const testConfig = this.buildTestConfig(options);

      console.log(chalk.cyan("🚀 Starting Automated API Load Test"));
      console.log(chalk.gray("─".repeat(60)));

      this.displayTestInfo(testConfig);

      // Get email list
      const emails = await this.emailService.getEmails(testConfig.maxEmails);

      if (emails.length === 0) {
        throw new Error("No emails available for testing");
      }

      console.log(
        chalk.green(`📧 Loaded ${emails.length} emails for testing\n`)
      );

      // Run the load test
      const results = await this.executeLoadTest(emails, testConfig);

      // Generate report
      const report = await this.reportService.generateReport(
        results,
        testConfig
      );

      // Save results
      await this.saveResults(report, testConfig);

      // Display summary
      this.displaySummary(report);

      console.log(chalk.green("\n✅ Load test completed successfully!"));

      return report;
    } catch (error) {
      logger.error("Load test failed:", error);
      throw error;
    } finally {
      this.isRunning = false;
    }
  }

  buildTestConfig(options) {
    const scenario =
      config.SCENARIOS[options.scenario?.toUpperCase()] ||
      config.SCENARIOS.MEDIUM_LOAD;

    return {
      apiUrl: options.url || config.API_URL,
      maxEmails: options.maxEmails || scenario.MAX_EMAILS_TO_TEST,
      concurrentRequests: options.concurrent || scenario.CONCURRENT_REQUESTS,
      delayBetweenBatches: options.delay || scenario.DELAY_BETWEEN_BATCHES,
      scenario: options.scenario || "medium",
      outputDir: options.output || path.join(process.cwd(), "logs"),
      verbose: options.verbose || false,
      requestTimeout: config.REQUEST_TIMEOUT,
      ...scenario,
    };
  }

  displayTestInfo(testConfig) {
    console.log(chalk.blue("📊 Test Configuration:"));
    console.log(`  🎯 Target URL: ${chalk.yellow(testConfig.apiUrl)}`);
    console.log(`  📧 Max Emails: ${chalk.yellow(testConfig.maxEmails)}`);
    console.log(
      `  ⚡ Concurrent Requests: ${chalk.yellow(testConfig.concurrentRequests)}`
    );
    console.log(
      `  ⏱️  Batch Delay: ${chalk.yellow(testConfig.delayBetweenBatches)}ms`
    );
    console.log(`  🏷️  Scenario: ${chalk.yellow(testConfig.scenario)}`);
    console.log("");
  }

  async executeLoadTest(emails, testConfig) {
    const batches = this.createBatches(emails, testConfig.concurrentRequests);

    console.log(chalk.blue(`📦 Processing ${batches.length} batches...\n`));

    const overallStats = {
      totalRequests: 0,
      successful: 0,
      failed: 0,
      errors: [],
      statusCodes: {},
      responseTimes: [],
      startTime: Date.now(),
      endTime: null,
      batches: [],
    };

    // Process each batch
    for (let i = 0; i < batches.length; i++) {
      const batchResults = await this.processBatch(
        batches[i],
        i + 1,
        testConfig
      );

      // Update overall stats
      overallStats.totalRequests += batches[i].length;
      overallStats.successful += batchResults.successful;
      overallStats.failed += batchResults.failed;
      overallStats.errors.push(...batchResults.errors);
      overallStats.responseTimes.push(...batchResults.responseTimes);
      overallStats.batches.push(batchResults);

      // Count status codes
      batchResults.responses.forEach((response) => {
        const code = response.statusCode;
        overallStats.statusCodes[code] =
          (overallStats.statusCodes[code] || 0) + 1;
      });

      this.displayBatchProgress(i + 1, batches.length, batchResults);

      // Wait between batches (except for the last one)
      if (i < batches.length - 1) {
        await this.delay(testConfig.delayBetweenBatches);
      }
    }

    overallStats.endTime = Date.now();
    return overallStats;
  }

  async processBatch(emails, batchNumber, testConfig) {
    const batchStartTime = Date.now();

    console.log(
      chalk.cyan(
        `⚡ Batch ${batchNumber}: Processing ${emails.length} requests...`
      )
    );

    // Create promises for all requests in this batch
    const promises = emails.map((email) =>
      this.apiService
        .makeRequest(email, testConfig)
        .then((result) => ({
          ...result,
          responseTime: Date.now() - batchStartTime,
        }))
    );

    // Execute all requests concurrently
    const results = await Promise.allSettled(promises);

    // Process results
    const batchStats = {
      batchNumber,
      emails: emails.length,
      successful: 0,
      failed: 0,
      errors: [],
      responses: [],
      responseTimes: [],
      duration: Date.now() - batchStartTime,
    };

    results.forEach((result, index) => {
      if (result.status === "fulfilled") {
        batchStats.successful++;
        batchStats.responses.push(result.value);
        batchStats.responseTimes.push(result.value.responseTime || 0);

        if (testConfig.verbose) {
          console.log(
            chalk.green(`  ✓ ${emails[index]}: ${result.value.statusCode}`)
          );
        }
      } else {
        batchStats.failed++;
        batchStats.errors.push({ email: emails[index], ...result.reason });

        if (testConfig.verbose) {
          console.log(
            chalk.red(`  ✗ ${emails[index]}: ${result.reason.error}`)
          );
        }
      }
    });

    return batchStats;
  }

  createBatches(emails, batchSize) {
    const batches = [];
    for (let i = 0; i < emails.length; i += batchSize) {
      batches.push(emails.slice(i, i + batchSize));
    }
    return batches;
  }

  displayBatchProgress(current, total, batchStats) {
    const percentage = ((current / total) * 100).toFixed(1);
    const progressBar = this.createProgressBar(current, total);

    console.log(
      chalk.blue(`${progressBar} ${percentage}% `) +
        chalk.green(`(${batchStats.successful} ✓`) +
        chalk.red(` ${batchStats.failed} ✗) `) +
        chalk.gray(`${batchStats.duration}ms`)
    );
  }

  createProgressBar(current, total, length = 20) {
    const filled = Math.round((current / total) * length);
    const empty = length - filled;
    return "█".repeat(filled) + "░".repeat(empty);
  }

  displaySummary(report) {
    const stats = report.stats;

    console.log(chalk.cyan("\n📊 TEST RESULTS SUMMARY"));
    console.log(chalk.gray("─".repeat(60)));
    console.log(
      `⏱️  Duration: ${chalk.yellow((stats.duration / 1000).toFixed(2))}s`
    );
    console.log(`📨 Total Requests: ${chalk.yellow(stats.totalRequests)}`);
    console.log(
      `✅ Success Rate: ${chalk.green(stats.successRate.toFixed(2))}%`
    );
    console.log(`❌ Error Rate: ${chalk.red(stats.errorRate.toFixed(2))}%`);
    console.log(
      `🚀 Requests/sec: ${chalk.yellow(stats.requestsPerSecond.toFixed(2))}`
    );
    console.log(
      `⚡ Avg Response Time: ${chalk.yellow(
        stats.avgResponseTime.toFixed(2)
      )}ms`
    );

    if (Object.keys(stats.statusCodes).length > 0) {
      console.log(chalk.blue("\n📈 Status Codes:"));
      Object.entries(stats.statusCodes).forEach(([code, count]) => {
        const color = code.startsWith("2")
          ? "green"
          : code.startsWith("4")
          ? "yellow"
          : "red";
        console.log(`  ${chalk[color](code)}: ${count} requests`);
      });
    }
  }

  async saveResults(report, testConfig) {
    const timestamp = format(new Date(), "yyyy-MM-dd_HH-mm-ss");
    const filename = `load-test-${testConfig.scenario}-${timestamp}.json`;
    const filepath = path.join(testConfig.outputDir, filename);

    await fs.ensureDir(testConfig.outputDir);
    await fs.writeJson(filepath, report, { spaces: 2 });

    console.log(chalk.gray(`\n💾 Results saved to: ${filepath}`));

    // Also save a summary CSV for easy analysis
    const csvPath = path.join(testConfig.outputDir, `summary-${timestamp}.csv`);
    await this.reportService.generateCsvSummary(report, csvPath);
  }

  delay(ms) {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }
}

module.exports = LoadTestController;
