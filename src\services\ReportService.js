const fs = require("fs-extra");
const path = require("path");
const { format } = require("date-fns");

class ReportService {
  constructor() {
    this.reportsDir = path.join(process.cwd(), "logs", "reports");
  }

  async generateReport(stats, testConfig) {
    const duration = stats.endTime - stats.startTime;
    const successRate = (stats.successful / stats.totalRequests) * 100;
    const errorRate = (stats.failed / stats.totalRequests) * 100;
    const requestsPerSecond = stats.totalRequests / (duration / 1000);

    const avgResponseTime =
      stats.responseTimes.length > 0
        ? stats.responseTimes.reduce((a, b) => a + b, 0) /
          stats.responseTimes.length
        : 0;

    const minResponseTime =
      stats.responseTimes.length > 0 ? Math.min(...stats.responseTimes) : 0;

    const maxResponseTime =
      stats.responseTimes.length > 0 ? Math.max(...stats.responseTimes) : 0;

    // Calculate percentiles
    const sortedResponseTimes = [...stats.responseTimes].sort((a, b) => a - b);
    const p50 = this.calculatePercentile(sortedResponseTimes, 50);
    const p95 = this.calculatePercentile(sortedResponseTimes, 95);
    const p99 = this.calculatePercentile(sortedResponseTimes, 99);

    const report = {
      metadata: {
        timestamp: new Date().toISOString(),
        testConfig,
        version: "2.0.0",
      },
      stats: {
        duration,
        totalRequests: stats.totalRequests,
        successful: stats.successful,
        failed: stats.failed,
        successRate,
        errorRate,
        requestsPerSecond,
        avgResponseTime,
        minResponseTime,
        maxResponseTime,
        responseTimePercentiles: {
          p50,
          p95,
          p99,
        },
        statusCodes: stats.statusCodes,
      },
      errors: this.aggregateErrors(stats.errors),
      batches: stats.batches.map((batch) => ({
        batchNumber: batch.batchNumber,
        emails: batch.emails,
        successful: batch.successful,
        failed: batch.failed,
        duration: batch.duration,
        avgResponseTime:
          batch.responseTimes.length > 0
            ? batch.responseTimes.reduce((a, b) => a + b, 0) /
              batch.responseTimes.length
            : 0,
      })),
      recommendations: this.generateRecommendations(stats, testConfig),
    };

    return report;
  }

  calculatePercentile(sortedArray, percentile) {
    if (sortedArray.length === 0) return 0;

    const index = Math.ceil((percentile / 100) * sortedArray.length) - 1;
    return sortedArray[Math.max(0, index)];
  }

  aggregateErrors(errors) {
    const errorCounts = {};
    const errorDetails = [];

    errors.forEach((error) => {
      const errorKey = error.error || "Unknown error";
      errorCounts[errorKey] = (errorCounts[errorKey] || 0) + 1;

      errorDetails.push({
        email: error.email,
        error: error.error,
        statusCode: error.statusCode,
        timestamp: error.timestamp,
      });
    });

    return {
      summary: errorCounts,
      details: errorDetails.slice(0, 100), // Limit to first 100 errors
    };
  }

  generateRecommendations(stats, testConfig) {
    const recommendations = [];
    const successRate = (stats.successful / stats.totalRequests) * 100;
    const avgResponseTime =
      stats.responseTimes.length > 0
        ? stats.responseTimes.reduce((a, b) => a + b, 0) /
          stats.responseTimes.length
        : 0;

    // Success rate recommendations
    if (successRate < 95) {
      recommendations.push({
        type: "warning",
        category: "success_rate",
        message: `Success rate is ${successRate.toFixed(
          2
        )}%. Consider investigating API errors.`,
      });
    }

    // Response time recommendations
    if (avgResponseTime > 2000) {
      recommendations.push({
        type: "warning",
        category: "performance",
        message: `Average response time is ${avgResponseTime.toFixed(
          2
        )}ms. Consider optimizing API performance.`,
      });
    }

    // Load recommendations
    if (testConfig.concurrentRequests < 5 && stats.totalRequests > 100) {
      recommendations.push({
        type: "info",
        category: "load_testing",
        message:
          "Consider increasing concurrent requests for more intensive load testing.",
      });
    }

    // Error pattern analysis
    const errorCounts = {};
    stats.errors.forEach((error) => {
      const errorKey = error.error || "Unknown";
      errorCounts[errorKey] = (errorCounts[errorKey] || 0) + 1;
    });

    const mostCommonError = Object.entries(errorCounts).sort(
      ([, a], [, b]) => b - a
    )[0];

    if (mostCommonError && mostCommonError[1] > stats.totalRequests * 0.1) {
      recommendations.push({
        type: "error",
        category: "error_analysis",
        message: `Most common error: "${mostCommonError[0]}" (${mostCommonError[1]} occurrences). Focus on fixing this issue.`,
      });
    }

    return recommendations;
  }

  async generateCsvSummary(report, outputPath) {
    const csvHeaders = [
      "Timestamp",
      "Scenario",
      "Total Requests",
      "Successful",
      "Failed",
      "Success Rate (%)",
      "Avg Response Time (ms)",
      "Requests/sec",
      "Duration (s)",
    ];

    const csvRow = [
      report.metadata.timestamp,
      report.metadata.testConfig.scenario,
      report.stats.totalRequests,
      report.stats.successful,
      report.stats.failed,
      report.stats.successRate.toFixed(2),
      report.stats.avgResponseTime.toFixed(2),
      report.stats.requestsPerSecond.toFixed(2),
      (report.stats.duration / 1000).toFixed(2),
    ];

    const csvContent = [csvHeaders.join(","), csvRow.join(",")].join("\n");

    await fs.ensureDir(path.dirname(outputPath));

    // Append to existing file or create new one
    if (await fs.pathExists(outputPath)) {
      await fs.appendFile(outputPath, "\n" + csvRow.join(","));
    } else {
      await fs.writeFile(outputPath, csvContent);
    }

    return outputPath;
  }

  async generateHtmlReport(report, outputPath = null) {
    const htmlPath =
      outputPath ||
      path.join(
        this.reportsDir,
        `report-${format(new Date(), "yyyy-MM-dd_HH-mm-ss")}.html`
      );

    const htmlContent = this.buildHtmlReport(report);

    await fs.ensureDir(path.dirname(htmlPath));
    await fs.writeFile(htmlPath, htmlContent);

    return htmlPath;
  }

  buildHtmlReport(report) {
    const { stats, metadata, errors, recommendations } = report;

    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Load Test Report - ${metadata.timestamp}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .stat-card { background: #f8f9fa; padding: 15px; border-radius: 6px; text-align: center; border-left: 4px solid #007bff; }
        .stat-value { font-size: 24px; font-weight: bold; color: #333; }
        .stat-label { color: #666; font-size: 14px; }
        .success { border-left-color: #28a745; }
        .warning { border-left-color: #ffc107; }
        .error { border-left-color: #dc3545; }
        .section { margin-bottom: 30px; }
        .section h3 { color: #333; border-bottom: 2px solid #007bff; padding-bottom: 5px; }
        table { width: 100%; border-collapse: collapse; margin-top: 10px; }
        th, td { padding: 8px 12px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #f8f9fa; font-weight: bold; }
        .recommendation { padding: 10px; margin: 10px 0; border-radius: 4px; }
        .recommendation.info { background-color: #d1ecf1; border-left: 4px solid #bee5eb; }
        .recommendation.warning { background-color: #fff3cd; border-left: 4px solid #ffeaa7; }
        .recommendation.error { background-color: #f8d7da; border-left: 4px solid #f5c6cb; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>API Load Test Report</h1>
            <p>Generated on ${new Date(metadata.timestamp).toLocaleString()}</p>
            <p>Scenario: <strong>${
              metadata.testConfig.scenario
            }</strong> | Target: <strong>${
      metadata.testConfig.apiUrl
    }</strong></p>
        </div>

        <div class="stats-grid">
            <div class="stat-card success">
                <div class="stat-value">${stats.totalRequests}</div>
                <div class="stat-label">Total Requests</div>
            </div>
            <div class="stat-card ${
              stats.successRate >= 95 ? "success" : "warning"
            }">
                <div class="stat-value">${stats.successRate.toFixed(1)}%</div>
                <div class="stat-label">Success Rate</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">${stats.requestsPerSecond.toFixed(
                  1
                )}</div>
                <div class="stat-label">Requests/sec</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">${stats.avgResponseTime.toFixed(
                  0
                )}ms</div>
                <div class="stat-label">Avg Response Time</div>
            </div>
        </div>

        <div class="section">
            <h3>Response Time Analysis</h3>
            <table>
                <tr><th>Metric</th><th>Value (ms)</th></tr>
                <tr><td>Minimum</td><td>${stats.minResponseTime}</td></tr>
                <tr><td>Average</td><td>${stats.avgResponseTime.toFixed(
                  2
                )}</td></tr>
                <tr><td>95th Percentile</td><td>${
                  stats.responseTimePercentiles.p95
                }</td></tr>
                <tr><td>99th Percentile</td><td>${
                  stats.responseTimePercentiles.p99
                }</td></tr>
                <tr><td>Maximum</td><td>${stats.maxResponseTime}</td></tr>
            </table>
        </div>

        <div class="section">
            <h3>Status Code Distribution</h3>
            <table>
                <tr><th>Status Code</th><th>Count</th><th>Percentage</th></tr>
                ${Object.entries(stats.statusCodes)
                  .map(
                    ([code, count]) =>
                      `<tr><td>${code}</td><td>${count}</td><td>${(
                        (count / stats.totalRequests) *
                        100
                      ).toFixed(2)}%</td></tr>`
                  )
                  .join("")}
            </table>
        </div>

        ${
          recommendations.length > 0
            ? `
        <div class="section">
            <h3>Recommendations</h3>
            ${recommendations
              .map(
                (rec) =>
                  `<div class="recommendation ${rec.type}"><strong>${rec.category}:</strong> ${rec.message}</div>`
              )
              .join("")}
        </div>
        `
            : ""
        }

        ${
          errors.summary && Object.keys(errors.summary).length > 0
            ? `
        <div class="section">
            <h3>Error Summary</h3>
            <table>
                <tr><th>Error Type</th><th>Count</th></tr>
                ${Object.entries(errors.summary)
                  .map(
                    ([error, count]) =>
                      `<tr><td>${error}</td><td>${count}</td></tr>`
                  )
                  .join("")}
            </table>
        </div>
        `
            : ""
        }
    </div>
</body>
</html>`;
  }

  async saveReport(report, format = "json") {
    const timestamp = format(new Date(), "yyyy-MM-dd_HH-mm-ss");
    const scenario = report.metadata.testConfig.scenario;

    await fs.ensureDir(this.reportsDir);

    if (format === "json" || format === "all") {
      const jsonPath = path.join(
        this.reportsDir,
        `${scenario}-${timestamp}.json`
      );
      await fs.writeJson(jsonPath, report, { spaces: 2 });
    }

    if (format === "html" || format === "all") {
      const htmlPath = await this.generateHtmlReport(report);
      return htmlPath;
    }

    if (format === "csv" || format === "all") {
      const csvPath = path.join(this.reportsDir, `summary-${timestamp}.csv`);
      await this.generateCsvSummary(report, csvPath);
    }

    return path.join(this.reportsDir, `${scenario}-${timestamp}.json`);
  }
}

module.exports = ReportService;
