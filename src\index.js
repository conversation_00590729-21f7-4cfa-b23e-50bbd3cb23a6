#!/usr/bin/env node

const { program } = require("commander");
const chalk = require("chalk");
const path = require("path");
require("dotenv").config();

const LoadTestController = require("./controllers/LoadTestController");
const config = require("../config");
const logger = require("./utils/logger");

// Configure CLI
program
  .name("api-load-tester")
  .description("Automated API load testing tool")
  .version("2.0.0");

program
  .option(
    "-s, --scenario <type>",
    "Test scenario (smoke, light, medium, heavy, stress)",
    "medium"
  )
  .option("-u, --url <url>", "API endpoint URL")
  .option(
    "-c, --concurrent <number>",
    "Number of concurrent requests",
    parseInt
  )
  .option("-m, --max-emails <number>", "Maximum emails to test", parseInt)
  .option("-d, --delay <number>", "Delay between batches (ms)", parseInt)
  .option("-o, --output <path>", "Output directory for results")
  .option("--schedule <cron>", "Schedule tests using cron expression")
  .option("--continuous", "Run continuous monitoring")
  .option("--dry-run", "Preview configuration without running tests")
  .option("--verbose", "Enable verbose logging");

program
  .command("run")
  .description("Run load test with specified configuration")
  .action(async (options) => {
    try {
      const controller = new LoadTestController();
      await controller.runTest(program.opts());
    } catch (error) {
      logger.error("Failed to run load test:", error);
      process.exit(1);
    }
  });

program
  .command("schedule")
  .description("Start scheduled testing")
  .option(
    "-c, --cron <expression>",
    "Cron expression for scheduling",
    "0 */6 * * *"
  )
  .action(async (options) => {
    const { default: Scheduler } = await import("./services/Scheduler.js");
    const scheduler = new Scheduler();
    scheduler.start(options.cron);
  });

program
  .command("monitor")
  .description("Start monitoring dashboard")
  .option(
    "-p, --port <number>",
    "Port for monitoring dashboard",
    parseInt,
    3000
  )
  .action(async (options) => {
    const { default: Monitor } = await import("./services/Monitor.js");
    const monitor = new Monitor();
    monitor.start(options.port);
  });

program
  .command("analyze")
  .description("Analyze test results")
  .option("-f, --file <path>", "Result file to analyze")
  .option(
    "-d, --days <number>",
    "Analyze results from last N days",
    parseInt,
    7
  )
  .action(async (options) => {
    const Analyzer = require("./utils/analyzer");
    const analyzer = new Analyzer();
    await analyzer.analyze(options);
  });

// Default action when no command is specified
program.action(async (options) => {
  console.log(chalk.cyan("🚀 API Load Testing Tool v2.0.0\n"));

  if (options.dryRun) {
    console.log(chalk.yellow("📋 Configuration Preview:"));
    console.log(JSON.stringify(options, null, 2));
    return;
  }

  try {
    const controller = new LoadTestController();
    await controller.runTest(options);
  } catch (error) {
    logger.error("Failed to run load test:", error);
    process.exit(1);
  }
});

// Error handling
process.on("uncaughtException", (error) => {
  logger.error("Uncaught Exception:", error);
  process.exit(1);
});

process.on("unhandledRejection", (reason, promise) => {
  logger.error("Unhandled Rejection at:", promise, "reason:", reason);
  process.exit(1);
});

// Graceful shutdown
process.on("SIGINT", () => {
  console.log(chalk.yellow("\n⚠️  Shutting down gracefully..."));
  process.exit(0);
});

program.parse();
