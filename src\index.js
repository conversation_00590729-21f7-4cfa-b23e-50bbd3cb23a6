import fs from "fs";
import axios from "axios";

const API_URL = "http://localhost:8000/api/auth/signup";
const EMAIL_FILE = "./src/emails.json";
const FAILED_FILE = "./failed.json";

const firstName = "Krishna";
const lastName = "Patel";
const password = "k<PERSON>hna@123";

const CONCURRENCY_LIMIT = 5;

async function signup(email) {
  try {
    const res = await axios.post(
      API_URL,
      {
        first_name: firstName,
        last_name: lastName,
        email,
        password,
        password_confirmation: password,
      },
      { headers: { "Content-Type": "application/json" } }
    );

    if (res.status === 200 || res.status === 201) {
      console.log(`✅ Success: ${email}`);
      return { email, success: true };
    } else {
      console.log(`❌ Failed (status ${res.status}): ${email}`);
      return { email, success: false };
    }
  } catch (err) {
    console.log(
      `❌ Error: ${email} (${err.response?.data?.message || err.message})`
    );
    return { email, success: false };
  }
}

async function processBatch(batch) {
  const results = await Promise.allSettled(batch.map(signup));
  return results.map((r) =>
    r.status === "fulfilled" ? r.value : { success: false, email: "unknown" }
  );
}

async function main() {
  const emails = JSON.parse(fs.readFileSync(EMAIL_FILE, "utf-8"));
  const failed = [];
  let successCount = 0;
  let failCount = 0;

  console.log(
    `🚀 Starting signup for ${emails.length} emails with concurrency = ${CONCURRENCY_LIMIT}\n`
  );

  for (let i = 0; i < emails.length; i += CONCURRENCY_LIMIT) {
    const chunk = emails.slice(i, i + CONCURRENCY_LIMIT);
    const results = await processBatch(chunk);

    for (const res of results) {
      if (res.success) successCount++;
      else {
        failCount++;
        failed.push(res.email);
      }
    }
  }

  fs.writeFileSync(FAILED_FILE, JSON.stringify(failed, null, 2));

  console.log("\n==============================");
  console.log(`✅ Success: ${successCount}`);
  console.log(`❌ Failed: ${failCount}`);
  console.log("==============================");
  console.log(`📁 Failed emails saved to ${FAILED_FILE}`);
}

main();
