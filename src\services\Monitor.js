const express = require("express");
const fs = require("fs-extra");
const path = require("path");
const chalk = require("chalk");
const LoadTestController = require("../controllers/LoadTestController");
const EmailService = require("./EmailService");
const logger = require("../utils/logger");

class Monitor {
  constructor() {
    this.app = express();
    this.controller = new LoadTestController();
    this.emailService = new EmailService();
    this.setupRoutes();
    this.setupMiddleware();
  }

  setupMiddleware() {
    this.app.use(express.json());
    this.app.use(express.static(path.join(__dirname, "../public")));

    // CORS for development
    this.app.use((req, res, next) => {
      res.header("Access-Control-Allow-Origin", "*");
      res.header(
        "Access-Control-Allow-Headers",
        "Origin, X-Requested-With, Content-Type, Accept"
      );
      res.header(
        "Access-Control-Allow-Methods",
        "GET, POST, PUT, DELETE, OPTIONS"
      );
      next();
    });

    // Request logging
    this.app.use((req, res, next) => {
      logger.info(`${req.method} ${req.path}`, {
        ip: req.ip,
        userAgent: req.get("User-Agent"),
      });
      next();
    });
  }

  setupRoutes() {
    // Dashboard homepage
    this.app.get("/", (req, res) => {
      res.send(this.getDashboardHtml());
    });

    // API endpoints
    this.app.get("/api/status", this.getStatus.bind(this));
    this.app.get("/api/results", this.getResults.bind(this));
    this.app.get("/api/results/:filename", this.getResultFile.bind(this));
    this.app.get("/api/emails/stats", this.getEmailStats.bind(this));

    // Test execution
    this.app.post("/api/test/run", this.runTest.bind(this));
    this.app.post("/api/test/stop", this.stopTest.bind(this));

    // Configuration
    this.app.get("/api/config", this.getConfig.bind(this));
    this.app.post("/api/config", this.updateConfig.bind(this));

    // Health check
    this.app.get("/health", (req, res) => {
      res.json({ status: "healthy", timestamp: new Date().toISOString() });
    });
  }

  async getStatus(req, res) {
    try {
      const emailStats = await this.emailService.getEmailStats();
      const recentResults = await this.getRecentResults(5);

      res.json({
        status: "running",
        testRunning: this.controller.isRunning || false,
        emailStats,
        recentResults: recentResults.map((r) => ({
          filename: path.basename(r.path),
          timestamp: r.stats.ctime,
          scenario: r.metadata?.testConfig?.scenario || "unknown",
          totalRequests: r.stats?.totalRequests || 0,
          successRate: r.stats?.successRate || 0,
        })),
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  async getResults(req, res) {
    try {
      const resultsDir = path.join(process.cwd(), "logs");
      const files = await fs.readdir(resultsDir);

      const results = await Promise.all(
        files
          .filter((file) => file.endsWith(".json"))
          .map(async (file) => {
            const filePath = path.join(resultsDir, file);
            const stats = await fs.stat(filePath);
            const data = await fs.readJson(filePath);

            return {
              filename: file,
              path: filePath,
              size: stats.size,
              modified: stats.mtime,
              summary: {
                totalRequests: data.stats?.totalRequests || 0,
                successRate: data.stats?.successRate || 0,
                scenario: data.metadata?.testConfig?.scenario || "unknown",
              },
            };
          })
      );

      results.sort((a, b) => new Date(b.modified) - new Date(a.modified));
      res.json(results);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  async getResultFile(req, res) {
    try {
      const filename = req.params.filename;
      const filePath = path.join(process.cwd(), "logs", filename);

      if (!(await fs.pathExists(filePath))) {
        return res.status(404).json({ error: "File not found" });
      }

      const data = await fs.readJson(filePath);
      res.json(data);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  async getEmailStats(req, res) {
    try {
      const stats = await this.emailService.getEmailStats();
      res.json(stats);
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  async runTest(req, res) {
    try {
      if (this.controller.isRunning) {
        return res.status(409).json({ error: "Test is already running" });
      }

      const options = req.body;

      // Run test asynchronously
      this.controller
        .runTest(options)
        .then((result) => {
          logger.info("Test completed via API", { result: result.stats });
        })
        .catch((error) => {
          logger.error("Test failed via API", { error: error.message });
        });

      res.json({
        message: "Test started",
        timestamp: new Date().toISOString(),
        options,
      });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  async stopTest(req, res) {
    try {
      // Note: This would need to be implemented in the controller
      // For now, we'll just return a message
      res.json({
        message: "Stop signal sent",
        note: "Test will complete current batch before stopping",
      });
    } catch (error) {
      res.status(500).json({ error: error.message });
    }
  }

  getConfig(req, res) {
    const config = require("../../config");
    res.json(config);
  }

  updateConfig(req, res) {
    // Note: This would require careful validation and restart logic
    res.json({
      message: "Configuration update not implemented",
      note: "Modify config.js file directly and restart the application",
    });
  }

  async getRecentResults(limit = 10) {
    try {
      const resultsDir = path.join(process.cwd(), "logs");
      const files = await fs.readdir(resultsDir);

      const jsonFiles = files.filter((file) => file.endsWith(".json"));
      const fileStats = await Promise.all(
        jsonFiles.map(async (file) => {
          const filePath = path.join(resultsDir, file);
          const stats = await fs.stat(filePath);
          return { path: filePath, stats };
        })
      );

      fileStats.sort((a, b) => b.stats.mtime - a.stats.mtime);
      return fileStats.slice(0, limit);
    } catch (error) {
      logger.error("Error getting recent results:", error);
      return [];
    }
  }

  getDashboardHtml() {
    return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Load Test Monitor</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; background: #f5f7fa; }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .header { text-align: center; margin-bottom: 30px; }
        .header h1 { color: #2c3e50; margin-bottom: 10px; }
        .status-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .card { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .card h3 { color: #34495e; margin-bottom: 15px; font-size: 18px; }
        .metric { display: flex; justify-content: space-between; margin-bottom: 10px; }
        .metric-label { color: #7f8c8d; }
        .metric-value { font-weight: bold; color: #2c3e50; }
        .btn { background: #3498db; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; font-size: 14px; }
        .btn:hover { background: #2980b9; }
        .btn-danger { background: #e74c3c; }
        .btn-danger:hover { background: #c0392b; }
        .btn-success { background: #27ae60; }
        .btn-success:hover { background: #219a52; }
        .results-table { width: 100%; border-collapse: collapse; margin-top: 15px; }
        .results-table th, .results-table td { padding: 12px; text-align: left; border-bottom: 1px solid #ecf0f1; }
        .results-table th { background: #f8f9fa; font-weight: 600; }
        .status-indicator { display: inline-block; width: 10px; height: 10px; border-radius: 50%; margin-right: 8px; }
        .status-running { background: #f39c12; }
        .status-idle { background: #95a5a6; }
        .status-success { background: #27ae60; }
        .refresh-btn { position: fixed; bottom: 20px; right: 20px; }
        .test-controls { display: flex; gap: 10px; margin-bottom: 20px; }
        .form-group { margin-bottom: 15px; }
        .form-group label { display: block; margin-bottom: 5px; color: #34495e; font-weight: 500; }
        .form-group select, .form-group input { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 API Load Test Monitor</h1>
            <p>Real-time monitoring and control dashboard</p>
        </div>

        <div class="status-grid">
            <div class="card">
                <h3>System Status</h3>
                <div class="metric">
                    <span class="metric-label">Status:</span>
                    <span class="metric-value" id="system-status">
                        <span class="status-indicator status-idle"></span>Loading...
                    </span>
                </div>
                <div class="metric">
                    <span class="metric-label">Uptime:</span>
                    <span class="metric-value" id="uptime">-</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Memory Usage:</span>
                    <span class="metric-value" id="memory">-</span>
                </div>
            </div>

            <div class="card">
                <h3>Email Statistics</h3>
                <div class="metric">
                    <span class="metric-label">Total Emails:</span>
                    <span class="metric-value" id="total-emails">-</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Available:</span>
                    <span class="metric-value" id="available-emails">-</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Domains:</span>
                    <span class="metric-value" id="email-domains">-</span>
                </div>
            </div>

            <div class="card">
                <h3>Test Controls</h3>
                <div class="form-group">
                    <label for="scenario-select">Scenario:</label>
                    <select id="scenario-select">
                        <option value="smoke">Smoke Test</option>
                        <option value="light">Light Load</option>
                        <option value="medium" selected>Medium Load</option>
                        <option value="heavy">Heavy Load</option>
                        <option value="stress">Stress Test</option>
                    </select>
                </div>
                <div class="test-controls">
                    <button class="btn btn-success" onclick="startTest()">Start Test</button>
                    <button class="btn btn-danger" onclick="stopTest()">Stop Test</button>
                </div>
            </div>
        </div>

        <div class="card">
            <h3>Recent Test Results</h3>
            <table class="results-table" id="results-table">
                <thead>
                    <tr>
                        <th>Timestamp</th>
                        <th>Scenario</th>
                        <th>Requests</th>
                        <th>Success Rate</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody id="results-body">
                    <tr><td colspan="5">Loading...</td></tr>
                </tbody>
            </table>
        </div>
    </div>

    <button class="btn refresh-btn" onclick="refreshData()">🔄 Refresh</button>

    <script>
        let refreshInterval;

        async function fetchData(endpoint) {
            try {
                const response = await fetch(endpoint);
                return await response.json();
            } catch (error) {
                console.error('Fetch error:', error);
                return null;
            }
        }

        async function updateStatus() {
            const status = await fetchData('/api/status');
            if (!status) return;

            // Update system status
            const statusElement = document.getElementById('system-status');
            const indicator = status.testRunning ? 'status-running' : 'status-idle';
            const statusText = status.testRunning ? 'Running Test' : 'Idle';
            statusElement.innerHTML = \`<span class="status-indicator \${indicator}"></span>\${statusText}\`;

            document.getElementById('uptime').textContent = \`\${Math.floor(status.uptime / 3600)}h \${Math.floor((status.uptime % 3600) / 60)}m\`;
            document.getElementById('memory').textContent = \`\${Math.round(status.memory.heapUsed / 1024 / 1024)}MB\`;

            // Update email stats
            if (status.emailStats) {
                document.getElementById('total-emails').textContent = status.emailStats.total;
                document.getElementById('available-emails').textContent = status.emailStats.available;
                document.getElementById('email-domains').textContent = status.emailStats.domains;
            }
        }

        async function updateResults() {
            const results = await fetchData('/api/results');
            if (!results) return;

            const tbody = document.getElementById('results-body');
            tbody.innerHTML = '';

            results.slice(0, 10).forEach(result => {
                const row = tbody.insertRow();
                row.innerHTML = \`
                    <td>\${new Date(result.modified).toLocaleString()}</td>
                    <td>\${result.summary.scenario}</td>
                    <td>\${result.summary.totalRequests}</td>
                    <td>\${result.summary.successRate.toFixed(1)}%</td>
                    <td><button class="btn" onclick="viewResult('\${result.filename}')">View</button></td>
                \`;
            });
        }

        async function startTest() {
            const scenario = document.getElementById('scenario-select').value;
            
            try {
                const response = await fetch('/api/test/run', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ scenario })
                });
                
                const result = await response.json();
                alert(result.message || 'Test started');
                refreshData();
            } catch (error) {
                alert('Failed to start test: ' + error.message);
            }
        }

        async function stopTest() {
            try {
                const response = await fetch('/api/test/stop', { method: 'POST' });
                const result = await response.json();
                alert(result.message || 'Stop signal sent');
            } catch (error) {
                alert('Failed to stop test: ' + error.message);
            }
        }

        function viewResult(filename) {
            window.open(\`/api/results/\${filename}\`, '_blank');
        }

        function refreshData() {
            updateStatus();
            updateResults();
        }

        // Initial load and set up auto-refresh
        refreshData();
        refreshInterval = setInterval(refreshData, 5000); // Refresh every 5 seconds

        // Cleanup on page unload
        window.addEventListener('beforeunload', () => {
            if (refreshInterval) clearInterval(refreshInterval);
        });
    </script>
</body>
</html>
    `;
  }

  start(port = 3000) {
    this.app.listen(port, () => {
      console.log(
        chalk.cyan(
          `📊 Monitor dashboard running at: ${chalk.yellow(
            `http://localhost:${port}`
          )}`
        )
      );
      console.log(chalk.gray("Available endpoints:"));
      console.log(chalk.gray(`  • Dashboard: http://localhost:${port}`));
      console.log(
        chalk.gray(`  • API Status: http://localhost:${port}/api/status`)
      );
      console.log(
        chalk.gray(`  • Test Results: http://localhost:${port}/api/results`)
      );
      console.log(
        chalk.gray(`  • Health Check: http://localhost:${port}/health`)
      );
    });

    return this.app;
  }
}

// CLI interface for the monitor
if (require.main === module) {
  const monitor = new Monitor();
  const port = process.argv[2] || 3000;

  monitor.start(port);
}

module.exports = Monitor;
