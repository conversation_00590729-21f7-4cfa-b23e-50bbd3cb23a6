const winston = require("winston");
const path = require("path");
const fs = require("fs-extra");

// Ensure logs directory exists
const logsDir = path.join(process.cwd(), "logs");
fs.ensureDirSync(logsDir);

// Define log format
const logFormat = winston.format.combine(
  winston.format.timestamp(),
  winston.format.errors({ stack: true }),
  winston.format.json()
);

// Console format for development
const consoleFormat = winston.format.combine(
  winston.format.colorize(),
  winston.format.timestamp({ format: "HH:mm:ss" }),
  winston.format.printf(({ timestamp, level, message, ...meta }) => {
    let metaStr = Object.keys(meta).length ? JSON.stringify(meta, null, 2) : "";
    return `${timestamp} [${level}] ${message} ${metaStr}`;
  })
);

// Create logger instance
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || "info",
  format: logFormat,
  defaultMeta: { service: "load-test" },
  transports: [
    // Error log - only errors
    new winston.transports.File({
      filename: path.join(logsDir, "error.log"),
      level: "error",
      maxsize: 5242880, // 5MB
      maxFiles: 5,
    }),

    // Combined log - all levels
    new winston.transports.File({
      filename: path.join(logsDir, "combined.log"),
      maxsize: 5242880, // 5MB
      maxFiles: 5,
    }),

    // Daily rotate logs
    new winston.transports.File({
      filename: path.join(
        logsDir,
        `app-${new Date().toISOString().split("T")[0]}.log`
      ),
      datePattern: "YYYY-MM-DD",
      maxsize: 5242880,
      maxFiles: "14d",
    }),
  ],
});

// Add console transport for development
if (process.env.NODE_ENV !== "production") {
  logger.add(
    new winston.transports.Console({
      format: consoleFormat,
    })
  );
}

// Add performance timing helper
logger.time = (label) => {
  const start = Date.now();
  return {
    end: (message = "") => {
      const duration = Date.now() - start;
      logger.info(`⏱️ ${label}: ${duration}ms ${message}`, { duration, label });
      return duration;
    },
  };
};

// Add structured logging helpers
logger.api = (message, data = {}) => {
  logger.info(message, { type: "api", ...data });
};

logger.test = (message, data = {}) => {
  logger.info(message, { type: "test", ...data });
};

logger.performance = (message, data = {}) => {
  logger.info(message, { type: "performance", ...data });
};

logger.security = (message, data = {}) => {
  logger.warn(message, { type: "security", ...data });
};

// Error handling for uncaught exceptions
logger.exceptions.handle(
  new winston.transports.File({
    filename: path.join(logsDir, "exceptions.log"),
  })
);

logger.rejections.handle(
  new winston.transports.File({
    filename: path.join(logsDir, "rejections.log"),
  })
);

module.exports = logger;
