const { CronJob } = require("cron");
const chalk = require("chalk");
const LoadTestController = require("../controllers/LoadTestController");
const config = require("../../config");
const logger = require("../utils/logger");

class Scheduler {
  constructor() {
    this.jobs = new Map();
    this.controller = new LoadTestController();
    this.isRunning = false;
  }

  start(cronExpression = "0 */6 * * *", testOptions = {}) {
    if (this.isRunning) {
      console.log(chalk.yellow("⚠️  Scheduler is already running"));
      return;
    }

    console.log(chalk.cyan("⏰ Starting automated test scheduler"));
    console.log(chalk.gray(`📅 Schedule: ${cronExpression}`));
    console.log(
      chalk.gray(`🎯 Test scenario: ${testOptions.scenario || "medium"}`)
    );

    const job = new CronJob(cronExpression, async () => {
      try {
        console.log(chalk.blue("\n🚀 Scheduled test starting..."));
        const timestamp = new Date().toISOString();

        const options = {
          scenario: "medium",
          verbose: false,
          output: `logs/scheduled-${timestamp.split("T")[0]}`,
          ...testOptions,
        };

        await this.controller.runTest(options);

        console.log(chalk.green("✅ Scheduled test completed successfully"));
        logger.info("Scheduled test completed", { timestamp, options });
      } catch (error) {
        console.error(chalk.red("❌ Scheduled test failed:"), error.message);
        logger.error("Scheduled test failed", {
          error: error.message,
          timestamp: new Date().toISOString(),
        });
      }
    });

    job.start();
    this.jobs.set("main", job);
    this.isRunning = true;

    console.log(chalk.green("✅ Scheduler started successfully"));
    console.log(chalk.gray("Press Ctrl+C to stop the scheduler"));

    // Handle graceful shutdown
    process.on("SIGINT", () => {
      this.stop();
      process.exit(0);
    });

    // Keep process alive
    this.keepAlive();
  }

  stop() {
    console.log(chalk.yellow("\n⏹️  Stopping scheduler..."));

    this.jobs.forEach((job, name) => {
      job.stop();
      console.log(chalk.gray(`  Stopped job: ${name}`));
    });

    this.jobs.clear();
    this.isRunning = false;

    console.log(chalk.green("✅ Scheduler stopped"));
  }

  addJob(name, cronExpression, testOptions) {
    if (this.jobs.has(name)) {
      throw new Error(`Job '${name}' already exists`);
    }

    const job = new CronJob(cronExpression, async () => {
      try {
        console.log(chalk.blue(`\n🔄 Running job: ${name}`));
        await this.controller.runTest(testOptions);
        console.log(chalk.green(`✅ Job '${name}' completed`));
      } catch (error) {
        console.error(chalk.red(`❌ Job '${name}' failed:`), error.message);
        logger.error(`Job ${name} failed`, { error: error.message });
      }
    });

    job.start();
    this.jobs.set(name, job);

    console.log(
      chalk.green(`➕ Added job '${name}' with schedule: ${cronExpression}`)
    );
  }

  removeJob(name) {
    const job = this.jobs.get(name);
    if (job) {
      job.stop();
      this.jobs.delete(name);
      console.log(chalk.yellow(`🗑️  Removed job: ${name}`));
      return true;
    }
    return false;
  }

  listJobs() {
    console.log(chalk.blue("\n📋 Active Jobs:"));

    if (this.jobs.size === 0) {
      console.log(chalk.gray("  No active jobs"));
      return;
    }

    this.jobs.forEach((job, name) => {
      const nextRun = job.nextDate();
      console.log(
        chalk.white(
          `  • ${name}: ${nextRun ? nextRun.toLocaleString() : "Unknown"}`
        )
      );
    });
  }

  setupMultipleScenarios() {
    const scenarios = [
      {
        name: "daily-light",
        cron: "0 9 * * *",
        options: { scenario: "light" },
      },
      {
        name: "hourly-smoke",
        cron: "0 * * * *",
        options: { scenario: "smoke" },
      },
      {
        name: "weekly-stress",
        cron: "0 3 * * 0",
        options: { scenario: "stress" },
      },
    ];

    scenarios.forEach(({ name, cron, options }) => {
      try {
        this.addJob(name, cron, options);
      } catch (error) {
        console.warn(
          chalk.yellow(`⚠️  Could not add job '${name}': ${error.message}`)
        );
      }
    });

    console.log(chalk.green("\n🎯 Multi-scenario scheduling configured"));
  }

  keepAlive() {
    // Keep the process alive and show status periodically
    setInterval(() => {
      if (this.isRunning) {
        console.log(
          chalk.gray(`⏱️  Scheduler running... (${this.jobs.size} jobs active)`)
        );
        this.listJobs();
      }
    }, 300000); // Every 5 minutes
  }

  // Predefined schedule configurations
  static getSchedulePresets() {
    return {
      "every-hour": "0 * * * *",
      "every-6-hours": "0 */6 * * *",
      "daily-9am": "0 9 * * *",
      "daily-midnight": "0 0 * * *",
      "weekdays-9am": "0 9 * * 1-5",
      "weekly-sunday": "0 3 * * 0",
      "every-15-minutes": "*/15 * * * *",
      "business-hours": "0 9-17 * * 1-5", // Every hour from 9 AM to 5 PM, Monday to Friday
    };
  }

  setPresetSchedule(presetName, testOptions = {}) {
    const presets = Scheduler.getSchedulePresets();
    const cronExpression = presets[presetName];

    if (!cronExpression) {
      throw new Error(
        `Unknown preset: ${presetName}. Available: ${Object.keys(presets).join(
          ", "
        )}`
      );
    }

    this.start(cronExpression, testOptions);
    console.log(chalk.green(`📅 Using preset schedule: ${presetName}`));
  }

  // Health monitoring for scheduled tests
  async runHealthCheck() {
    const ApiService = require("./ApiService");
    const apiService = new ApiService();

    try {
      const healthResult = await apiService.healthCheck();

      if (!healthResult.healthy) {
        console.log(
          chalk.red("❌ API health check failed - skipping scheduled test")
        );
        logger.warn(
          "Scheduled test skipped due to API health check failure",
          healthResult
        );
        return false;
      }

      console.log(chalk.green("✅ API health check passed"));
      return true;
    } catch (error) {
      console.log(chalk.red("❌ Health check error - proceeding with caution"));
      logger.warn("Health check error", { error: error.message });
      return true; // Proceed anyway, but log the issue
    }
  }
}

// CLI interface for the scheduler
if (require.main === module) {
  const scheduler = new Scheduler();

  const args = process.argv.slice(2);
  const cronExpression = args[0] || "0 */6 * * *";
  const scenario = args[1] || "medium";

  console.log(chalk.cyan("🚀 API Load Test Scheduler v2.0"));
  console.log(chalk.gray("─".repeat(50)));

  scheduler.start(cronExpression, { scenario });
}

module.exports = Scheduler;
