{"metadata": {"timestamp": "2025-10-05T05:08:37.850Z", "testConfig": {"apiUrl": "https://api.foodforsoul.in/api/auth/signup", "maxEmails": 100, "concurrentRequests": 10, "delayBetweenBatches": 1000, "scenario": "stress", "outputDir": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Load test\\logs", "verbose": false, "requestTimeout": 30000, "MAX_EMAILS_TO_TEST": 100, "CONCURRENT_REQUESTS": 10, "DELAY_BETWEEN_BATCHES": 1000}, "version": "2.0.0"}, "stats": {"duration": 10080, "totalRequests": 100, "successful": 100, "failed": 0, "successRate": 100, "errorRate": 0, "requestsPerSecond": 9.920634920634921, "avgResponseTime": 86.82, "minResponseTime": 30, "maxResponseTime": 409, "responseTimePercentiles": {"p50": 47, "p95": 396, "p99": 406}, "statusCodes": {"409": 100}}, "errors": {"summary": {}, "details": []}, "batches": [{"batchNumber": 1, "emails": 10, "successful": 10, "failed": 0, "duration": 409, "avgResponseTime": 398.4}, {"batchNumber": 2, "emails": 10, "successful": 10, "failed": 0, "duration": 64, "avgResponseTime": 54.3}, {"batchNumber": 3, "emails": 10, "successful": 10, "failed": 0, "duration": 63, "avgResponseTime": 50.3}, {"batchNumber": 4, "emails": 10, "successful": 10, "failed": 0, "duration": 55, "avgResponseTime": 46.1}, {"batchNumber": 5, "emails": 10, "successful": 10, "failed": 0, "duration": 46, "avgResponseTime": 38.8}, {"batchNumber": 6, "emails": 10, "successful": 10, "failed": 0, "duration": 54, "avgResponseTime": 42.9}, {"batchNumber": 7, "emails": 10, "successful": 10, "failed": 0, "duration": 51, "avgResponseTime": 43.3}, {"batchNumber": 8, "emails": 10, "successful": 10, "failed": 0, "duration": 82, "avgResponseTime": 46.8}, {"batchNumber": 9, "emails": 10, "successful": 10, "failed": 0, "duration": 129, "avgResponseTime": 108.5}, {"batchNumber": 10, "emails": 10, "successful": 10, "failed": 0, "duration": 47, "avgResponseTime": 38.8}], "recommendations": []}