// Email Conversion Script
// This script converts the legacy mail-list.js format to the new JSON format

const fs = require("fs-extra");
const path = require("path");
const chalk = require("chalk");

async function convertEmails() {
  try {
    console.log(chalk.cyan("📧 Converting email list to new format..."));

    // Load legacy email list
    const legacyPath = path.join(__dirname, "../../mail-list.js");
    if (!(await fs.pathExists(legacyPath))) {
      console.log(chalk.red("❌ Legacy email list not found at:", legacyPath));
      return;
    }

    delete require.cache[require.resolve(legacyPath)];
    const emailList = require(legacyPath);

    if (!Array.isArray(emailList)) {
      throw new Error("Email list is not an array");
    }

    // Validate and clean emails
    const validEmails = emailList.filter((email) => {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      return typeof email === "string" && emailRegex.test(email.trim());
    });

    console.log(
      chalk.blue(
        `📊 Found ${emailList.length} emails, ${validEmails.length} valid`
      )
    );

    // Remove duplicates
    const uniqueEmails = [
      ...new Set(validEmails.map((email) => email.trim().toLowerCase())),
    ];
    console.log(
      chalk.blue(
        `🔄 Removed ${validEmails.length - uniqueEmails.length} duplicates`
      )
    );

    // Save to new format
    const dataDir = path.join(__dirname, "../../data");
    const outputPath = path.join(dataDir, "emails.json");

    await fs.ensureDir(dataDir);
    await fs.writeJson(outputPath, uniqueEmails, { spaces: 2 });

    console.log(
      chalk.green(
        `✅ Converted ${uniqueEmails.length} emails to: ${outputPath}`
      )
    );

    // Generate statistics
    const domains = {};
    uniqueEmails.forEach((email) => {
      const domain = email.split("@")[1];
      domains[domain] = (domains[domain] || 0) + 1;
    });

    console.log(chalk.blue("\n📈 Email Statistics:"));
    console.log(`  Total unique emails: ${uniqueEmails.length}`);
    console.log(`  Unique domains: ${Object.keys(domains).length}`);

    const topDomains = Object.entries(domains)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 10);

    console.log(chalk.blue("\n🏆 Top 10 Domains:"));
    topDomains.forEach(([domain, count]) => {
      console.log(`  ${domain}: ${count} emails`);
    });

    // Save statistics
    const statsPath = path.join(dataDir, "email-stats.json");
    await fs.writeJson(
      statsPath,
      {
        total: uniqueEmails.length,
        domains: Object.keys(domains).length,
        topDomains,
        lastUpdated: new Date().toISOString(),
      },
      { spaces: 2 }
    );

    console.log(chalk.gray(`\n💾 Statistics saved to: ${statsPath}`));
  } catch (error) {
    console.error(chalk.red("❌ Conversion failed:"), error.message);
    process.exit(1);
  }
}

// Run if this file is executed directly
if (require.main === module) {
  convertEmails();
}

module.exports = { convertEmails };
