@echo off
echo.
echo ================================================
echo   API Load Testing Tool v2.0 - Quick Start
echo ================================================
echo.
echo Choose an option:
echo   1. Run Smoke Test (5 emails)
echo   2. Run Medium Load Test (100 emails)  
echo   3. Run Heavy Load Test (500 emails)
echo   4. Start Monitoring Dashboard
echo   5. Start Automated Scheduler
echo   6. Analyze Recent Results
echo   7. Convert Email List
echo   8. View Help
echo.
set /p choice="Enter your choice (1-8): "

if "%choice%"=="1" (
    echo Running Smoke Test...
    npm run test
    goto end
)
if "%choice%"=="2" (
    echo Running Medium Load Test...
    npm run test:medium
    goto end
)
if "%choice%"=="3" (
    echo Running Heavy Load Test...
    npm run test:heavy
    goto end
)
if "%choice%"=="4" (
    echo Starting Monitoring Dashboard...
    echo Dashboard will be available at: http://localhost:3000
    node src/index.js monitor
    goto end
)
if "%choice%"=="5" (
    echo Starting Automated Scheduler...
    node src/index.js schedule
    goto end
)
if "%choice%"=="6" (
    echo Analyzing Recent Results...
    npm run analyze
    goto end
)
if "%choice%"=="7" (
    echo Converting Email List...
    npm run convert-emails
    goto end
)
if "%choice%"=="8" (
    echo.
    echo ===============================
    echo   Command Line Usage Examples
    echo ===============================
    echo.
    echo Basic Commands:
    echo   npm start                    - Run default test
    echo   npm run test                 - Run smoke test
    echo   npm run test:medium          - Run medium load test
    echo   npm run monitor              - Start dashboard
    echo   npm run schedule             - Start scheduler
    echo.
    echo Advanced Commands:
    echo   node src/index.js --scenario stress --concurrent 50
    echo   node src/index.js monitor --port 8080
    echo   node src/index.js schedule --cron "0 */2 * * *"
    echo   node src/index.js analyze --days 30
    echo.
    echo Dashboard URL: http://localhost:3000
    echo.
    pause
    goto start
)

echo Invalid choice. Please select 1-8.
pause
goto start

:end
echo.
echo Process completed. Press any key to exit...
pause > nul

:start