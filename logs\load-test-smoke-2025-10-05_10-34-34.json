{"metadata": {"timestamp": "2025-10-05T05:04:34.825Z", "testConfig": {"apiUrl": "https://api.foodforsoul.in/api/auth/signup", "maxEmails": 100, "concurrentRequests": 10, "delayBetweenBatches": 1000, "scenario": "smoke", "outputDir": "C:\\Users\\<USER>\\OneDrive\\Desktop\\Load test\\logs", "verbose": false, "requestTimeout": 30000, "MAX_EMAILS_TO_TEST": 100, "CONCURRENT_REQUESTS": 10, "DELAY_BETWEEN_BATCHES": 1000}, "version": "2.0.0"}, "stats": {"duration": 13811, "totalRequests": 100, "successful": 100, "failed": 0, "successRate": 100, "errorRate": 0, "requestsPerSecond": 7.240605314604301, "avgResponseTime": 397.19, "minResponseTime": 168, "maxResponseTime": 1689, "responseTimePercentiles": {"p50": 260, "p95": 1679, "p99": 1687}, "statusCodes": {"201": 100}}, "errors": {"summary": {}, "details": []}, "batches": [{"batchNumber": 1, "emails": 10, "successful": 10, "failed": 0, "duration": 1689, "avgResponseTime": 1627.5}, {"batchNumber": 2, "emails": 10, "successful": 10, "failed": 0, "duration": 283, "avgResponseTime": 219.4}, {"batchNumber": 3, "emails": 10, "successful": 10, "failed": 0, "duration": 289, "avgResponseTime": 237.8}, {"batchNumber": 4, "emails": 10, "successful": 10, "failed": 0, "duration": 459, "avgResponseTime": 260.4}, {"batchNumber": 5, "emails": 10, "successful": 10, "failed": 0, "duration": 313, "avgResponseTime": 258.9}, {"batchNumber": 6, "emails": 10, "successful": 10, "failed": 0, "duration": 288, "avgResponseTime": 252}, {"batchNumber": 7, "emails": 10, "successful": 10, "failed": 0, "duration": 279, "avgResponseTime": 235}, {"batchNumber": 8, "emails": 10, "successful": 10, "failed": 0, "duration": 289, "avgResponseTime": 236.7}, {"batchNumber": 9, "emails": 10, "successful": 10, "failed": 0, "duration": 493, "avgResponseTime": 346.7}, {"batchNumber": 10, "emails": 10, "successful": 10, "failed": 0, "duration": 351, "avgResponseTime": 297.5}], "recommendations": []}