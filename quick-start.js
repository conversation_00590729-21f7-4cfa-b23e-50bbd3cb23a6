#!/usr/bin/env node

// Quick Start Script for API Load Testing Tool
const chalk = require("chalk");
const { spawn } = require("child_process");
const path = require("path");

console.log(chalk.cyan("🚀 API Load Testing Tool - Quick Start\n"));

const choices = [
  {
    key: "1",
    name: "Run Smoke Test (5 emails)",
    command: ["npm", "run", "test"],
  },
  {
    key: "2",
    name: "Run Medium Load Test (100 emails)",
    command: ["npm", "run", "test:medium"],
  },
  {
    key: "3",
    name: "Start Monitoring Dashboard",
    command: ["node", "src/index.js", "monitor"],
  },
  {
    key: "4",
    name: "Start Automated Scheduler",
    command: ["node", "src/index.js", "schedule"],
  },
  {
    key: "5",
    name: "Analyze Recent Results",
    command: ["npm", "run", "analyze"],
  },
  {
    key: "6",
    name: "Convert Email List",
    command: ["npm", "run", "convert-emails"],
  },
  {
    key: "7",
    name: "View Configuration",
    command: [
      "node",
      "-e",
      'console.log(JSON.stringify(require("./config.js"), null, 2))',
    ],
  },
];

console.log(chalk.blue("Choose an option:"));
choices.forEach((choice) => {
  console.log(chalk.white(`  ${choice.key}. ${choice.name}`));
});

console.log(chalk.gray("\nUsage examples:"));
console.log(chalk.gray("  Quick test:     npm run test"));
console.log(
  chalk.gray(
    "  Custom test:    node src/index.js --scenario heavy --concurrent 20"
  )
);
console.log(chalk.gray("  Dashboard:      http://localhost:3000"));
console.log(
  chalk.gray(
    '  Schedule:       node src/index.js schedule --cron "0 */2 * * *"'
  )
);

process.stdin.setRawMode(true);
process.stdin.resume();
process.stdin.setEncoding("utf8");

process.stdin.on("data", (key) => {
  const choice = choices.find((c) => c.key === key.trim());

  if (choice) {
    console.log(chalk.green(`\n▶️  Running: ${choice.name}\n`));

    const child = spawn(choice.command[0], choice.command.slice(1), {
      stdio: "inherit",
      shell: true,
      cwd: process.cwd(),
    });

    child.on("close", (code) => {
      console.log(chalk.gray(`\n✨ Process exited with code ${code}`));
      process.exit(code);
    });
  } else if (key === "\u0003" || key === "q") {
    // Ctrl+C or 'q'
    console.log(chalk.yellow("\n👋 Goodbye!"));
    process.exit(0);
  } else {
    console.log(chalk.red("Invalid choice. Press 1-7 or Ctrl+C to exit."));
  }
});

console.log(chalk.gray("\nPress 1-7 to select, or Ctrl+C to exit"));
