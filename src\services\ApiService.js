const axios = require("axios");
const config = require("../../config");
const logger = require("../utils/logger");

class ApiService {
  constructor() {
    this.client = axios.create({
      timeout: config.REQUEST_TIMEOUT,
      headers: {
        "Content-Type": "application/json",
        "User-Agent": config.USER_AGENT,
        ...config.CUSTOM_HEADERS,
      },
    });

    // Add response interceptor for logging
    this.client.interceptors.response.use(
      (response) => response,
      (error) => {
        if (config.LOGGING.LOG_ERRORS) {
          logger.error("API Request failed:", {
            url: error.config?.url,
            method: error.config?.method,
            status: error.response?.status,
            message: error.message,
          });
        }
        return Promise.reject(error);
      }
    );
  }

  generateUserData(email) {
    const emailParts = email.split("@")[0].split(".");
    const firstName = emailParts[0] || "Test";
    const lastName = emailParts[1] || "User";

    return {
      email: email,
      password: config.DEFAULT_PASSWORD,
      password_confirmation: config.DEFAULT_PASSWORD,
      first_name: firstName.charAt(0).toUpperCase() + firstName.slice(1),
      last_name: lastName.charAt(0).toUpperCase() + lastName.slice(1),
      // Add additional fields as needed
      phone: this.generatePhoneNumber(),
      birth_date: this.generateBirthDate(),
      terms_accepted: true,
    };
  }

  async makeRequest(email, testConfig = {}) {
    const startTime = Date.now();

    try {
      const userData = this.generateUserData(email);
      const url = testConfig.apiUrl || config.API_URL;

      if (config.LOGGING.VERBOSE) {
        logger.info(`Making request for: ${email}`);
      }

      const response = await this.client.post(url, userData);

      const responseTime = Date.now() - startTime;

      if (config.LOGGING.LOG_RESPONSES) {
        logger.info("API Response:", {
          email,
          status: response.status,
          responseTime,
          data: response.data,
        });
      }

      return {
        email,
        statusCode: response.status,
        headers: response.headers,
        body: response.data,
        responseTime,
        success: true,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      const responseTime = Date.now() - startTime;

      const errorResponse = {
        email,
        error: error.message,
        statusCode: error.response?.status || 0,
        responseTime,
        success: false,
        timestamp: new Date().toISOString(),
      };

      if (error.response) {
        errorResponse.headers = error.response.headers;
        errorResponse.body = error.response.data;
      }

      return errorResponse;
    }
  }

  async makeCustomRequest(url, method = "GET", data = null, headers = {}) {
    try {
      const response = await this.client({
        url,
        method,
        data,
        headers: { ...this.client.defaults.headers, ...headers },
      });

      return {
        statusCode: response.status,
        headers: response.headers,
        body: response.data,
        success: true,
      };
    } catch (error) {
      throw {
        statusCode: error.response?.status || 0,
        error: error.message,
        body: error.response?.data,
        success: false,
      };
    }
  }

  generatePhoneNumber() {
    const areaCode = Math.floor(Math.random() * 900) + 100;
    const prefix = Math.floor(Math.random() * 900) + 100;
    const lineNumber = Math.floor(Math.random() * 9000) + 1000;
    return `+1${areaCode}${prefix}${lineNumber}`;
  }

  generateBirthDate() {
    const start = new Date(1970, 0, 1);
    const end = new Date(2000, 11, 31);
    const randomDate = new Date(
      start.getTime() + Math.random() * (end.getTime() - start.getTime())
    );
    return randomDate.toISOString().split("T")[0];
  }

  async healthCheck(url = null) {
    try {
      const targetUrl = url || config.API_URL.replace("/signup", "/health");
      const response = await this.client.get(targetUrl);
      return {
        healthy: true,
        status: response.status,
        responseTime: response.headers["x-response-time"] || "N/A",
      };
    } catch (error) {
      return {
        healthy: false,
        error: error.message,
        status: error.response?.status || 0,
      };
    }
  }

  async validateApiEndpoint(url = null) {
    const targetUrl = url || config.API_URL;

    try {
      // Try OPTIONS request first to check CORS and allowed methods
      const optionsResponse = await this.client.options(targetUrl);

      return {
        valid: true,
        allowedMethods:
          optionsResponse.headers["access-control-allow-methods"] || "N/A",
        corsEnabled: !!optionsResponse.headers["access-control-allow-origin"],
      };
    } catch (error) {
      if (error.response?.status === 405) {
        // Method not allowed is okay, endpoint exists
        return {
          valid: true,
          note: "OPTIONS not supported but endpoint exists",
        };
      }

      return {
        valid: false,
        error: error.message,
        status: error.response?.status || 0,
      };
    }
  }
}

module.exports = ApiService;
